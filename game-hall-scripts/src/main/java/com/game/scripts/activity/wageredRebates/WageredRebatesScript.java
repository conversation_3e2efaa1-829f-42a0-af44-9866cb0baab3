package com.game.scripts.activity.wageredRebates;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_WageredRebates;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.*;
import com.game.entity.player.Player;
import com.game.entity.player.activity.wageredRebates.WageredRebatesInfo;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisRanking;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import io.lettuce.core.ScoredValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class WageredRebatesScript implements IActivityScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(WageredRebatesScript.class);

    @Override
    public void initData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_WageredRebates c_wageredRebates = merchantData.findC_WageredRebates(this.getClass().getSimpleName(), ActivityMrg.WAGERED_REBATES);
        if (c_wageredRebates == null) {
            return;
        }

        final WageredRebatesInfo wageredRebates = player.getWageredRebatesInfo();
        if (wageredRebates.isStart() && wageredRebates.getC_id() == c_wageredRebates.getC_id()) {
            return;
        }

        wageredRebates.reset();
        wageredRebates.setStart(true);
        wageredRebates.setC_id(c_wageredRebates.getC_id());
        final long endTime = TimeUtil.getTimeEndOfToday(TimeUtil.currentTimeMillis(), player.getTimeZone());
        wageredRebates.setEndTime(endTime);

        EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateWageredRebatesInfo(player.getPlayerId(), wageredRebates);
    }

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_WageredRebates c_wageredRebates = merchantData.findC_WageredRebates(this.getClass().getSimpleName(), ActivityMrg.WAGERED_REBATES);
        if (c_wageredRebates == null) {
            return;
        }

        final WageredRebatesInfo wageredRebates = player.getWageredRebatesInfo();

        int topNum = 0;
        final long lastTime = TimeUtil.currentTimeMillis() - TimeUtil.DAY;
        if (c_wageredRebates.getRankType() == 1) {// 比例
            final long totalNum = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zcard(RedisRanking.RANKING_WAGEREDREBATES.getKey(player.getBusiness_no(), TimeUtil.getDateTimeFormat(lastTime, TimeUtil.YYYYMMDD, c_wageredRebates.getTimeZone()))));
            topNum = (int) BigDecimalUtils.mul((double) totalNum, c_wageredRebates.getRankNum(), 9);
            topNum = Math.max(10, topNum);
        } else {//
            topNum = (int) c_wageredRebates.getRankNum();
        }

        Long ranking = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().zrevrank(RedisRanking.RANKING_WAGEREDREBATES.getKey(player.getBusiness_no(), TimeUtil.getDateTimeFormat(lastTime, TimeUtil.YYYYMMDD, player.getTimeZone())), player.getCurrencyId() + "_" + player.getPlayerId()));
        if (ranking == null) {
            ranking = 0L;
        } else {
            ranking++;
        }

        if (ranking != 0 && ranking <= topNum) {
            final double wagered = wageredRebates.getWageredMap().getOrDefault(player.getCurrencyId(), 0d);
            final double loss = wageredRebates.getLossMap().getOrDefault(player.getCurrencyId(), 0d);

            final double customerLoss = BigDecimalUtils.sub(wagered, loss, 4);
            double bonus = 0;
            if (customerLoss > 0) {//输分
                bonus = BigDecimalUtils.mul(customerLoss, c_wageredRebates.getLosePlayer(), 9);
            } else {
                bonus = BigDecimalUtils.mul(Math.abs(customerLoss), c_wageredRebates.getWinPlayer(), 9);
            }
            if (bonus > c_wageredRebates.getRewardLimit()) {
                bonus = c_wageredRebates.getRewardLimit();
            }
            wageredRebates.getBonusMap().put(player.getCurrencyId(), bonus);
        }

        wageredRebates.setLastEndTime(wageredRebates.getEndTime());
        wageredRebates.setStart(false);

        initData(player, null);
    }

    @Override
    public void execute(Player player, int rankType, int currencyId, double validAmount, double progress) {
        final WageredRebatesInfo wageredRebates = player.getWageredRebatesInfo();

        initData(player, null);

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_WageredRebates c_wageredRebates = merchantData.findC_WageredRebates(this.getClass().getSimpleName(), ActivityMrg.WAGERED_REBATES);
        if (c_wageredRebates == null) {
            return;
        }

        wageredRebates.incWagered(currencyId, validAmount);
        wageredRebates.incLoss(currencyId, progress);

        if (validAmount > 0) {
            final String rankDate = TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, c_wageredRebates.getTimeZone());

            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.zincrby(RedisRanking.RANKING_WAGEREDREBATES.getKey(player.getBusiness_no(), rankDate), validAmount, currencyId + "_" + player.getPlayerId()));
        }
    }

    @Override
    public void insertRobot() {
        if (Config.SERVER_ID != 4000) {
            return;
        }

        if (TimeUtil.getMinute() % 14 != 0) {
            return;
        }

        final List<String> business_noList = DataHallMrg.getInstance().getC_baseHostMerchantMap()
                .values().stream().map(C_BaseMerchant::getBusiness_no).distinct().toList();

        for (final String business_no : business_noList) {
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                continue;
            }

            final C_WageredRebates c_wageredRebates = merchantData.findC_WageredRebates(this.getClass().getSimpleName(), ActivityMrg.WAGERED_REBATES);
            if (c_wageredRebates == null) {
                continue;
            }

            final String currencyId = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initCurrencyId");

            final String rankDate = TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, c_wageredRebates.getTimeZone());
            final List<ScoredValue<String>> scoredValues = RedisPoolManager.getInstance().function(jedis -> jedis.sync()
                    .zrevrangeWithScores(RedisRanking.RANKING_WAGEREDREBATES.getKey(business_no, rankDate), 0, -1));

            int topNum = 0;
            if (c_wageredRebates.getRankType() == 1) {// 比例
                final long totalNum = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().zcard(RedisRanking.RANKING_WAGEREDREBATES.getKey(business_no, TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, c_wageredRebates.getTimeZone()))));
                topNum = (int) BigDecimalUtils.mul((double) totalNum, c_wageredRebates.getRankNum(), 9);
                topNum = Math.max(10, topNum);
            } else {//
                topNum = (int) c_wageredRebates.getRankNum();
            }

            //不足插入
            final Random rand = new Random();
            double robot = topNum - scoredValues.size();
            if (robot > 0) {
                for (int i = 0; i < robot; i++) {
                    final double randomDelta = (int) (rand.nextDouble() * 300) + 1; // 1.0 ~ 6.0

                    WageredPlayer bot = new WageredPlayer(randomId(), BigDecimalUtils.round(randomDelta, 2), 0);
                    RedisPoolManager.getInstance().executeAsync(cmd ->
                            cmd.zincrby(RedisRanking.RANKING_WAGEREDREBATES
                                    .getKey(business_no, rankDate), bot.flow, currencyId + "_" + bot.playerId));
                }
                continue;
            }

            final List<WageredPlayer> players = new ArrayList<>();
            final List<Long> playerIds = new ArrayList<>();
            for (final ScoredValue<String> scoredValue : scoredValues) {
                final String element = scoredValue.getValue();
                final long playerId = Long.parseLong(element.split("_")[1]);
                playerIds.add(playerId);
                if (String.valueOf(playerId).length() < 8) {
                    players.add(new WageredPlayer(playerId, scoredValue.getScore(), 0));
                }
            }

            final List<Player> playerList = EntityDaoMrg.getInstance().getDao(PlayerDao.class).loadPlayersByIds(playerIds);
            for (final Player player : playerList) {
                final WageredRebatesInfo wageredRebatesInfo = player.getWageredRebatesInfo();
                final double wagered = wageredRebatesInfo.getWageredMap().getOrDefault(player.getCurrencyId(), 0d);
                final double loss = wageredRebatesInfo.getLossMap().getOrDefault(player.getCurrencyId(), 0d);
                players.add(new WageredPlayer(player.getPlayerId(), wagered, loss));
            }

            // 计算插入账户
            final Result result = calculateInsertAccounts(players, topNum, c_wageredRebates.getWinPlayer(), c_wageredRebates.getLosePlayer());

            LOGGER.warn("result：{}", JsonUtils.writeAsJson(result));

            if (result.insertInex < 0) {
                continue;
            }

            int topSize = Math.max(10, topNum);
            if (topSize >= 200) {
                topSize = 200;
            }

            final ScoredValue<String> member = scoredValues.get(result.insertInex);
            ScoredValue<String> member1 = null;
            if (result.insertInex > 1) {
                member1 = scoredValues.get(result.insertInex - 1);
            }

            // ---------- 计算榜尾基准 ----------
            double lowScore = member.getScore();               // 插入起点（第 insertInex 名）
            double highScore = (member1 != null) ? member1.getScore() : lowScore + 50;

            // 构造机器人并插入 Redis，流水加入随机偏移
            int inserted = 0;            // 已插入机器人计数
            while (inserted < topSize - result.insertInex) {

                final double gap = highScore - lowScore;

                double robotFlow = highScore + 1 + rand.nextDouble() * (gap * 0.1);

                final WageredPlayer bot = new WageredPlayer(randomId(), BigDecimalUtils.round(robotFlow, 2), 0);
                RedisPoolManager.getInstance().executeAsync(cmd ->
                        cmd.zincrby(RedisRanking.RANKING_WAGEREDREBATES
                                .getKey(business_no, rankDate), bot.flow, currencyId + "_" + bot.playerId));
                inserted++;             // 只有真正写入时才 ++
            }
            LOGGER.warn("------------------------inserted：{}------------------------", inserted);
        }
    }

    @Override
    public void updateBackstage() {
        if (Config.SERVER_ID != 4000) {
            return;
        }

        if (TimeUtil.getMinute() % 5 != 0) {
            return;
        }

        final List<String> business_noList = DataHallMrg.getInstance().getC_baseHostMerchantMap()
                .values().stream().map(C_BaseMerchant::getBusiness_no).distinct().toList();

        for (final String business_no : business_noList) {
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                continue;
            }

            final C_WageredRebates c_wageredRebates = merchantData.findC_WageredRebates(this.getClass().getSimpleName(), ActivityMrg.WAGERED_REBATES);
            if (c_wageredRebates == null) {
                continue;
            }

            final String currencyId = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initCurrencyId");
            final String rankDate = TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, c_wageredRebates.getTimeZone());

            int topNum = 0;
            if (c_wageredRebates.getRankType() == 1) {// 比例
                final long totalNum = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().zcard(RedisRanking.RANKING_WAGEREDREBATES.getKey(business_no, rankDate)));
                topNum = (int) BigDecimalUtils.mul((double) totalNum, c_wageredRebates.getRankNum(), 9);
                topNum = Math.max(10, topNum);
            } else {//
                topNum = (int) c_wageredRebates.getRankNum();
            }

            int finalTopNum = topNum;
            final List<ScoredValue<String>> players = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zrevrangeWithScores(RedisRanking.RANKING_WAGEREDREBATES.getKey(business_no, rankDate), 0, finalTopNum - 1));
            int ranking = 0;
            final long uniqueId = HallServer.getInstance().getUniqueIDGenerator().nextId();
            if (players != null) {
                for (ScoredValue<String> tuple : players) {
                    ++ranking;
                    final double score = tuple.getScore();

                    final String element = tuple.getValue();
                    final long playerId = Long.parseLong(element.split("_")[1]);

                    final GameLog platform_playerWageredRebatesRankLog = new GameLog("platform_playerWageredRebatesRankLog");
                    platform_playerWageredRebatesRankLog.append("number", GuidGeneratorUtils.generateOrderId())
                            .append("business_no", business_no)
                            .append("timeZone", c_wageredRebates.getTimeZone())
                            .append("robot", String.valueOf(playerId).length() < 8)
                            .append("date", rankDate)
                            .append("playerId", playerId)
                            .append("ranking", ranking)
                            .append("currencyId", currencyId)
                            .append("wagered", score)
                            .append("uniqueId", uniqueId)
                            .append("logTime", TimeUtil.currentTimeMillis());
                    HallServer.getInstance().getLogProducerMrg().send(platform_playerWageredRebatesRankLog);
                }
            }
        }

    }

    public static int randomId() {
        final Random random = new Random();
        // 生成 1000000 到 9999999 之间的随机数（包含）
        return 1000000 + random.nextInt(9000000);
    }

    public static class WageredPlayer {
        long playerId;
        double flow; // 投注流水
        double loss; // 客损（亏损）

        public WageredPlayer(long playerId, double flow, double loss) {
            this.playerId = playerId;
            this.flow = flow;
            this.loss = loss;
        }

        boolean isRobot() {
            return String.valueOf(playerId).length() < 8;
        }
    }

    public static class Result {
        public int insertInex;
        public double requiredFlow;
        public double totalRewardCost;
        public double totalPlatformRevenue;
    }

    public static Result calculateInsertAccounts(List<WageredPlayer> realPlayers,
                                                 int topNum, double winRate, double loseRate) {

        // 1) 计算榜单规模：至少 10 人
        int topSize = Math.max(10, topNum);
        if (topSize >= 200) {
            topSize = 200;
        }

        // 2) 先按流水降序
        List<WageredPlayer> sorted = new ArrayList<>(realPlayers);
        sorted.sort((a, b) -> Double.compare(b.flow, a.flow));

        // --- 计算榜外平台净收益 --------------------
        double platformRevOutside = 0;
        for (int i = topSize; i < sorted.size(); i++) {
            WageredPlayer p = sorted.get(i);
            if (p.isRobot()) {
                continue;
            }
            double delta = sorted.get(i).flow - sorted.get(i).loss;
            if (delta > 0) {
                platformRevOutside += delta * loseRate;   // 玩家输钱 → 平台返损
            } else {
                platformRevOutside += Math.abs(delta) * winRate; // 玩家赢钱 → 平台发奖励
            }
        }

        // --- 计算原榜单（不含新机器人）奖励 & 返损 --------------------
        double rewardCostTop = 0, lossRefundTop = 0;
        int insertInex = -1;
        for (int i = 0; i < Math.min(topSize, sorted.size()); i++) {
            WageredPlayer p = sorted.get(i);
            if (p.isRobot()) {
                continue;
            }

            double delta = p.flow - p.loss;
            if (delta > 0) {
                lossRefundTop += delta * loseRate;   // 玩家输钱 → 平台返损
            } else {
                rewardCostTop += Math.abs(delta) * winRate; // 玩家赢钱 → 平台发奖励
            }

            if (platformRevOutside <= rewardCostTop + lossRefundTop) {
                insertInex = i;
                break;
            }
        }

        // --- 封装返回 --------------------
        Result r = new Result();
        r.insertInex = insertInex;
        r.totalRewardCost = rewardCostTop + lossRefundTop;
        r.totalPlatformRevenue = platformRevOutside;
        return r;
    }


    public static void main(String[] args) {
        List<WageredPlayer> players = new ArrayList<>();
        // 示例：生成1000个玩家，随机投注与客损
//        Random rand = new Random();
//        for (int i = 0; i < 500; i++) {
//            double flow = 100 + rand.nextDouble() * 900; // 投注在100–1000之间
//            double loss = flow * rand.nextDouble(); // 客损在 0–flow 之间
//            players.add(new WageredPlayer(randomId(), flow, loss));
//        }
        // 计算插入账户
//        Result result = calculateInsertAccounts(players, , 10, 1.0, 1.0);
        // 输出结果
//        System.out.println("✅ 插入平台账户数量: " + result.insertCount);
//        System.out.println("✅ 每个插入账户需要设置流水: " + result.requiredFlow);
//        System.out.println("💰 原始排行榜成本: " + result.totalRewardCost);
//        System.out.println("📈 平台真实可控收入: " + result.totalPlatformRevenue);

        double robotScore;
        double highScore = 25406.18;
        double lowScore = 25359.18;
        final Random rand = new Random();
        double gap = highScore - lowScore;
        double roll = rand.nextDouble();

        robotScore = highScore + 1 + rand.nextDouble() * ((100 + gap) * 0.1);
        System.out.println(robotScore);
    }
}
