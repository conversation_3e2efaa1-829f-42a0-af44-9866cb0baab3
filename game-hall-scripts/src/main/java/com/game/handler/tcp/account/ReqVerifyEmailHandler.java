package com.game.handler.tcp.account;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqVerifyAccount_VALUE, msg = HallMessage.ReqVerifyAccountMessage.class)
public class ReqVerifyEmailHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqVerifyEmailHandler.class);

    @Override
    public void run() {
        final HallMessage.ResVerifyAccountMessage.Builder res = HallMessage.ResVerifyAccountMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResVerifyAccount_VALUE);

        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                replyWithUdpSessionId(res.build());
                return;
            }

            final HallMessage.ReqVerifyAccountMessage req = (HallMessage.ReqVerifyAccountMessage) getMessage();
            final int threeParty = req.getThreeParty();
            final String areaCode = req.getAreaCode().trim();
            final String reqAccount = req.getAccount().trim();
            final int codeType = req.getCodeType();
            final String verifyCode = req.getVerifyCode().trim();

            String ac;
            if (threeParty == ThreeParty.Phone.getThreeParty()) {
                ac = areaCode + "-" + reqAccount;
            } else {
                ac = reqAccount;
            }

            //邮箱验证码
            final String finalAc = ac;
            final String code = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisLogin.Platform_LG_Account_VerifyCode.getKey(player.getBusiness_no(), codeType, finalAc)));
            if (StringUtil.isNullOrEmpty(verifyCode) || !verifyCode.equals(code)) {
                LOGGER.warn("account：{}，codeType：{}，verifyCode，{}-{}，", finalAc, codeType, code, verifyCode);
                res.setError(ErrorCode.VerifyCode_Error.getCode());
                replyWithUdpSessionId(res.build());
                return;
            }

            res.setThreeParty(threeParty);
            replyWithUdpSessionId(res.build());
        } catch (Exception e) {
            LOGGER.error("ReqVerifyAccountHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            replyWithUdpSessionId(res.build());
        }
    }
}
