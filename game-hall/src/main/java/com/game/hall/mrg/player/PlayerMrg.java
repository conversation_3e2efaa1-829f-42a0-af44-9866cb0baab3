package com.game.hall.mrg.player;

import com.game.dao.account.AccountDao;
import com.game.dao.player.PlayerDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.*;
import com.game.entity.account.Account;
import com.game.entity.player.Player;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.ReferralCode;
import com.game.enums.*;
import com.game.enums.redis.RedisLogin;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.IGameScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.*;
import io.lettuce.core.ScriptOutputType;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

public class PlayerMrg {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerMrg.class);

    private static final PlayerMrg instance = new PlayerMrg();

    public static PlayerMrg getInstance() {
        return instance;
    }

    private final Map<Long, Player> onlinePlayerMap = new ConcurrentHashMap<>(100);

    public Map<Long, Player> getOnlinePlayerMap() {
        return onlinePlayerMap;
    }

    public Player getOnlinePlayer(long playerId, String className) {
        final Player player = onlinePlayerMap.get(playerId);
        if (player == null) {
            LOGGER.warn("className：{}，playerId：{}，not exist", className, playerId);
            return null;
        }
        return player;
    }

    public void addOnlinePlayer(Player player) {
        this.onlinePlayerMap.put(player.getPlayerId(), player);
        LOGGER.info("business_no：{}，player，[{}-{}]，entry hall，ip：{}，onlineNum：{}", player.getBusiness_no(),
                player.getPlayerName(), player.getPlayerId(), player.getIp(), onlinePlayerMap.size());
    }

    public void removeOnlinePlayer(Player player) {
        this.onlinePlayerMap.remove(player.getPlayerId());
    }

    public Account findDbAccount(long accountId) {
        return EntityDaoMrg.getInstance().getDao(AccountDao.class).getById(accountId);
    }

    public Player findDbPlayer(long playerId) {
        final Player player = onlinePlayerMap.get(playerId);
        if (player == null) {
            return EntityDaoMrg.getInstance().getDao(PlayerDao.class).getById(playerId);
        }
        return player;
    }

    public PlayerPromote findDbPlayerPromote(long playerId) {
        final Player player = onlinePlayerMap.get(playerId);
        if (player == null) {
            return EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).getById(playerId);
        }
        return player.getPlayerPromote();
    }

    public void signOut(Player player) {
        try {
            removeOnlinePlayer(player);
            exitPlayer(player);
        } catch (Exception e) {
            LOGGER.warn("", e);
        }
    }

    private void exitPlayer(Player player) {
        try {
            ScriptLoader.getInstance().consumerScript("PlayerQuitScript",
                    (IPlayerScript script) -> script.quitHall(player));
        } catch (Exception e) {
            LOGGER.warn("exitPlayer", e);
        }
    }

    public void sendKickOutPlayerMsg(Player player) {
        final TcpMessage.ResTcpKickOutPlayerMessage.Builder res = TcpMessage.ResTcpKickOutPlayerMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTcpKickOutPlayer_VALUE)
                .setReason(0);
        player.sendMsg(res.build());
    }

    public void forEachPlayer(Consumer<Player> consumer) {
        for (final Player player : this.onlinePlayerMap.values()) {
            HallServer.getInstance().asyncExecute(player.getPlayerId(), () -> {
                consumer.accept(player);
            });
        }
    }

    /**
     * 每分钟检测
     */
    public void everyMinuteCheck() {
        ScriptLoader.getInstance().consumerScript("GameScript",
                IGameScript::gameEveryMinReset);

        ScriptLoader.getInstance().consumerScript("PlayerResetScript",
                IPlayerScript::everyMinuteCheck);
    }

    /**
     * 每天重置
     */
    public static void everyDayReset(Player player) {
        ScriptLoader.getInstance().consumerScript("PlayerResetScript",
                (IPlayerScript script) -> script.everyDayReset(player));
    }

    public static void everyDayTwoReset(Player player) {

    }

    public static void everyWeeklyReset(Player player) {
        ScriptLoader.getInstance().consumerScript("PlayerResetScript",
                (IPlayerScript script) -> script.everyWeeklyReset(player));
    }

    /**
     * 每月重置
     */
    public static void everyMonthReset(Player player) {
        ScriptLoader.getInstance().consumerScript("PlayerResetScript",
                (IPlayerScript script) -> script.everyMonthReset(player));
    }

    public static void responseHttp(Map<String, Object> params, Channel session, long pid) {
        try {
            final InnerMessage.InnerResHttpHandlerMessage.Builder res = InnerMessage.InnerResHttpHandlerMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.InnerResHttpHandler_VALUE)
                    .setParams(JsonUtils.writeAsJson(params));
            MsgUtil.sendInnerMsg(session, res.build(), pid);
        } catch (Exception e) {
            LOGGER.error("responseHttp", e);
        }
    }

    public static void responseHttp(int error, Channel session, long pid) {
        final Map<String, Object> httpRes = new LinkedHashMap<>();
        try {
            httpRes.put("error", error);
            final InnerMessage.InnerResHttpHandlerMessage.Builder res = InnerMessage.InnerResHttpHandlerMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.InnerResHttpHandler_VALUE)
                    .setParams(JsonUtils.writeAsJson(httpRes));
            MsgUtil.sendInnerMsg(session, res.build(), pid);
        } catch (Exception e) {
            LOGGER.error("responseHttp", e);
        }
    }

    public void playerGoldChangeLog(Player player, int currencyType, double beforeGold, double changeGold, double afterGold, Object reason) {
        final GameLog playerGoldChangeLog = new GameLog("platform_playerGoldChangeLog");
        playerGoldChangeLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("site", player.getWebSite())
                .append("business_no", player.getBusiness_no())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("region", player.getRegisterRegion())
                .append("currencyId", currencyType)
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("mediaId", player.getMediaId())
                .append("adId", player.getAdId());
        if (reason instanceof SpendReason spendReason) {
            playerGoldChangeLog.append("reasonType", 1)
                    .append("reason", spendReason.getReason())
                    .append("content", spendReason.getSource());
        }
        if (reason instanceof RewardReason rewardReason) {
            playerGoldChangeLog.append("reasonType", 2)
                    .append("reason", rewardReason.getReason())
                    .append("content", rewardReason.getSource());
        }
        playerGoldChangeLog.append("beforeGold", beforeGold)
                .append("changeGold", changeGold)
                .append("afterGold", afterGold)
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerGoldChangeLog);
    }

    public ReferralCode createReferralCode(Player player, String campaignName) {
        final ReferralCode referralCode = new ReferralCode();
        try {
            String code = ScriptLoader.getInstance().functionScript("PlayerGenerateInvitationCode",
                    (IPlayerScript script) -> script.generateInvitationCode(player.getPlayerId()));
            if (Objects.equals(code, player.getPlayerId() + "")) {
                code = "";
            }
            referralCode.setName(campaignName);
            referralCode.setCode(StringUtil.isNullOrEmpty(code) ? "" : code);
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData != null) {
                final String referralLink = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralLink");
                final String url = referralLink + "?" + "referralCode=" + referralCode.getCode();
                referralCode.setLink(url);
            }
            return referralCode;
        } catch (Exception e) {
            LOGGER.error("createReferralCode", e);
        }
        return referralCode;
    }

    public void timerCheckHear() {
        try {
            if (onlinePlayerMap.isEmpty()) {
                return;
            }

            final long currentTime = TimeUtil.currentTimeMillis();
            Iterator<Map.Entry<Long, Player>> iterator = onlinePlayerMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, Player> entry = iterator.next();
                final Player player = entry.getValue();
                if (player != null && currentTime >= player.getHeartbeat()) {
                    iterator.remove();  // Use iterator's remove method to avoid ConcurrentModificationException
                    LOGGER.info("playerId：{}，timerCheckHear exit", player.getPlayerId());
                    HallServer.getInstance().asyncExecute(player.getPlayerId(),
                            () -> exitPlayer(player));
                }
            }
        } catch (Exception e) {
            LOGGER.error("timerCheckHear", e);
        }
    }

    public void timerSavePlayer() {
        try {
            forEachPlayer(this::saveDBPlayer);
        } catch (Exception e) {
            LOGGER.error("timerSavePlayer", e);
        }
    }

    public void saveDBPlayer(Player player) {
        ScriptLoader.getInstance().consumerScript("PlayerSaveScript",
                (IPlayerScript script) -> script.savePlayer(player));
    }

    public void timerSaveRealPlayer() {
        try {
            forEachPlayer(this::saveRealDBPlayer);
        } catch (Exception e) {
            LOGGER.error("timerSavePlayer", e);
        }
    }

    public void saveRealDBPlayer(Player player) {
        ScriptLoader.getInstance().consumerScript("PlayerSaveScript",
                (IPlayerScript script) -> script.saveRealPlayer(player));
    }

}
